// Test setup file
import { beforeAll } from 'vitest';
import { env } from 'cloudflare:test';

// Setup global test environment
beforeAll(async () => {
  // Setup D1 database schema for tests
  try {
    // Create applications table
    await env.DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS applications (
        uuid TEXT PRIMARY KEY,
        version INTEGER NOT NULL,
        status TEXT NOT NULL,
        domain TEXT NOT NULL,
        preQualifyFields TEXT NOT NULL,
        approvalAmount INTEGER DEFAULT 0,
        agent TEXT,
        reason TEXT,
        applicationFields TEXT,
        pandadoc TEXT,
        utm TEXT,
        meta TEXT,
        fastTrack INTEGER NOT NULL DEFAULT 0,
        created_at TEXT DEFAULT (datetime('now')),
        updated_at TEXT DEFAULT (datetime('now')),
        started_at TEXT,
        submitted_at TEXT,
        signed_at TEXT,
        completed_at TEXT
      )
    `
    ).run();

    // Create meta table
    await env.DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS meta (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        created_at TEXT DEFAULT (datetime('now')),
        updated_at TEXT DEFAULT (datetime('now'))
      )
    `
    ).run();

    // Insert default round-robin data
    await env.DB.prepare(
      `INSERT OR IGNORE INTO meta (key, value) VALUES
        ('round-robin:index', '0'),
        ('agents:list', '[]')`
    ).run();

    // Create the trigger for auto-updating updated_at
    await env.DB.prepare(
      `
      CREATE TRIGGER IF NOT EXISTS update_applications_updated_at
      AFTER UPDATE ON applications
      FOR EACH ROW
      BEGIN
        UPDATE applications SET updated_at = datetime('now') WHERE uuid = OLD.uuid;
      END
    `
    ).run();

    console.log('D1 database schema setup complete');
  } catch (error) {
    console.warn('D1 setup failed (this is expected if DB binding is not available):', error.message);
  }

  console.log('Setting up test environment...');
});

// Test data generators
export const generatePrequalData = (overrides = {}) => ({
  fundingAmount: 75000,
  purpose: 'Expansion',
  topPriority: 'cost',
  timeline: 'month',
  businessName: 'Test Business LLC',
  monthlyRevenue: '************',
  businessStartDate: '2020-01-01', // More than 6 months ago
  firstName: 'Test',
  lastName: 'User',
  email: '<EMAIL>',
  phone: '1234567890',
  estimatedFICO: '700-780', // Above 550
  consent: true,
  ...overrides,
});

export const generateCreateAppRequest = (overrides = {}) => ({
  preQualifyFields: generatePrequalData(),
  domain: 'app.pinnaclefunding.com',
  ...overrides,
});

export const generateDeniedPrequalData = () => ({
  ...generatePrequalData(),
  businessStartDate: new Date(new Date().setMonth(new Date().getMonth() - 3)).toISOString().split('T')[0], // Less than 6 months ago
  monthlyRevenue: '0-10000', // Less than $10k
  estimatedFICO: '300-550', // Below 550
});

export const generateApplicationData = (overrides = {}) => ({
  businessName: 'Test Business LLC',
  dbaName: 'Test DBA',
  website: 'www.example.com',
  entityType: 'LLC',
  ein: '123456789',
  industry: 'Technology',
  businessStartDate: '2020-01-01',
  businessPhone: '1234567890',
  businessEmail: '<EMAIL>',
  address: {
    line1: '123 Test St',
    line2: 'Suite 100',
    city: 'Test City',
    state: 'NY',
    zip: '12345',
  },
  owners: [
    {
      firstName: 'Test',
      lastName: 'Owner',
      dateOfBirth: '1980-01-01',
      ssn: '123456789',
      email: '<EMAIL>',
      phone: '9876543210',
      address: {
        line1: '456 Owner St',
        city: 'Owner City',
        state: 'CA',
        zip: '54321',
      },
      ownershipPercentage: 100,
    },
  ],
  ...overrides,
});

// Test API utilities
export const BASE_URL = 'http://localhost:8787';
export const api = {
  get: async (path) => {
    return fetch(`${BASE_URL}${path}`);
  },
  post: async (path, data) => {
    return fetch(`${BASE_URL}${path}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
};
