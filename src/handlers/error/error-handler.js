import { logErrorToKV } from '../../kv/logs.js';
import { AppError } from '../../utils/helpers.js';

export const errorHandler = async (err, c) => {
  const headers = Object.fromEntries(c.req.raw.headers);
  const body = headers['content-type']?.includes('application/json') ? await c.req.bodyCache.json : await c.req.bodyCache.text;

  const { errorId } = await logErrorToKV(c.env, err, {
    headers,
    cf: c.req.raw.cf,
    requestBody: body || null,
    source: err?.source || 'errorHandler',
    statusCode: err?.statusCode || 500,
  });

  if (err instanceof AppError) {
    console.error(err);
    return c.json({ errorId, error: err.message }, err.statusCode);
  } else {
    console.error(err);
    return c.json({ errorId, error: 'Internal Server Error' }, 500);
  }
};
