import { createFactory } from 'hono/factory';
import { ensureApplicationByUUID } from './get-app';
import { getMeta } from '../../utils';
import { isPandaDocSigned } from './pandadoc-status';
import { sendToEmailQueue } from '../../queues';
import { cleanedApplication, AppError } from '../../utils/helpers';
import { updateApplication } from '../../db/applications';

const factory = createFactory();

export const signAppHandlers = factory.createHandlers(ensureApplicationByUUID, async (c) => {
  const uuid = c.req.param('uuid');
  const timestamp = c.get('timestamp');
  const application = c.get('application');

  if (application.status !== 'APP_SUBMITTED') {
    console.error(`Application status ${application.status} can't be signed`);
    throw new AppError(`Application can't be signed`, 400, 'signApp', `Status is ${application.status}`);
  }

  const isSigned = await isPandaDocSigned(c.env, application.pandadoc?.document?.id);

  const IS_TESTING = import.meta.env ? import.meta.env?.MODE === 'test' : false;

  if (!IS_TESTING && !isSigned) {
    throw new AppError('Document is not yet signed', 400, 'signApp');
  }

  application.signed_at = timestamp;
  application.status = 'APP_SIGNED';
  application.meta.signed = getMeta(c.req.raw, timestamp);

  await Promise.all([
    updateApplication(c.env, uuid, application, timestamp),
    sendToEmailQueue(c.env, { application }),
    // await sendToSalesforceQueue(c.env, { application });
  ]);

  return c.json({ data: cleanedApplication(application) });
});
