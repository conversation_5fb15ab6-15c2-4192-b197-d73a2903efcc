import { createFactory } from 'hono/factory';
import { ensureApplicationByUUID } from './get-app';
import { moveDocumentToDraft } from '../../pandadoc';
import { cleanedApplication, AppError } from '../../utils/helpers';
import { updateApplication } from '../../db/applications';

const factory = createFactory();

export const editAppHandlers = factory.createHandlers(ensureApplicationByUUID, async (c) => {
  const timestamp = c.get('timestamp');
  const application = c.get('application');

  if (application.status === 'APP_SUBMITTED') {
    application.status = 'APP_EDITING';
    const document = await moveDocumentToDraft(c.env, application.pandadoc?.document?.id);
    application.pandadoc = { document };
    await updateApplication(c.env, application.uuid, application, timestamp);

    return c.json({ data: cleanedApplication(application) });
  } else {
    console.error(`Application status ${application.status} can't be edited`);
    throw new AppError(`Application can't be edited`, 400, 'editApp', `Status is ${application.status}`);
  }
});
